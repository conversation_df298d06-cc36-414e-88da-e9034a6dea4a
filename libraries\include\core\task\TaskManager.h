#pragma once

#include "core/Exception.h"
#include "core/ThreadManager.h"
#include "core/task/AsyncTask.h"
#include "core/task/TaskFuture.h"

#include <BS_thread_pool.hpp>

namespace sparkle
{
// A typical async task life cycle:
// 1. call TaskManager::DispatchTask from any thread.
// 2. a task is generated along with a future to monitor its completion.
// 3. the task is dispatched to the target thread.
//    3.1. for game thread and render thread, it will be staged and run next frame.
//    3.2. for worker thread, it will be dispatched to thread pool immediately.
// 4. DispatchTask returns a future which you can attach callbacks to it.
// 5. a standalone TaskDispatcher will monitor all task futures and dispatch callbacks to target threads.
class TaskManager
{
public:
    explicit TaskManager(unsigned int max_task_threads);

    ~TaskManager()
    {
        is_valid_ = false;
        ConsumeThreadTasks(ThreadName::Worker);
    }

    static std::unique_ptr<TaskManager> CreateInstance(unsigned int max_task_threads);

    static TaskManager &Instance()
    {
        ASSERT_F(instance_, "TaskManager is not initialized. Do not use it before AppFramework::InitCore");
        return *instance_;
    }

    std::shared_ptr<TaskFuture> DispatchTask(std::function<void()> &&task, ThreadName thread_name);

    void ConsumeThreadTasks(ThreadName thread_name);

    std::vector<std::function<void()>> PopRenderThreadTasks()
    {
        return render_thread_tasks_.PopTasks();
    }

    static auto RunInMainThread(std::function<void()> &&task)
    {
        return TaskManager::Instance().DispatchTask(std::move(task), ThreadName::Main);
    }

    static auto RunInRenderThread(std::function<void()> &&task)
    {
        return TaskManager::Instance().DispatchTask(std::move(task), ThreadName::Render);
    }

    static auto RunInWorkerThread(std::function<void()> &&task)
    {
        return TaskManager::Instance().DispatchTask(std::move(task), ThreadName::Worker);
    }

    template <typename F> static auto ParallelFor(unsigned first_index, unsigned index_after_last, F &&task)
    {
        return TaskManager::Instance().GetThreadPool().submit_loop(first_index, index_after_last, std::forward(task));
    }

private:
    BS::light_thread_pool &GetThreadPool()
    {
        ASSERT(is_valid_);
        return *worker_thread_pool_;
    }

    struct ThreadTaskQueue
    {
        std::vector<std::function<void()>> tasks;
        std::mutex mutex;

        std::future<void> AddTask(std::function<void()> &&task);

        void RunAll();

        std::vector<std::function<void()>> PopTasks();
    };

    std::unique_ptr<BS::light_thread_pool> worker_thread_pool_;

    ThreadTaskQueue main_thread_tasks_;
    ThreadTaskQueue render_thread_tasks_;

    std::vector<std::shared_ptr<TaskFuture>> pending_tasks_;

    bool is_valid_ = true;

    static TaskManager *instance_;
};

} // namespace sparkle

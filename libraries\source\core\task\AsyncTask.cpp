#include "core/task/AsyncTask.h"

#include "core/task/TaskFuture.h"
#include "core/task/TaskManager.h"

namespace sparkle
{
AsyncTask::AsyncTask(std::function<void()> &&task, TargetThread target_thread)
    : task_(std::move(task)), target_thread_(target_thread)
{
    future_ = std::make_unique<TaskFuture>(target_thread);
}

void AsyncTask::Dispatch()
{
    future_->Setup(TaskManager::Instance().DispatchTask(std::move(task_), future_->GetTargetThreadName()));
    // register
}

TaskFuture &AsyncTask::Then(std::function<void()> &&task, TargetThread target_thread)
{
    return future_->Then(std::move(task), target_thread);
}
} // namespace sparkle

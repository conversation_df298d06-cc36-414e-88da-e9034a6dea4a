#include <core/task/TaskFuture.h>

#include <core/ThreadManager.h>
#include <core/task/TaskManager.h>

namespace sparkle
{
TaskFuture &TaskFuture::Then(std::function<void()> &&callback, AsyncTask::TargetThread thread)
{
    return callbacks_.emplace_back(std::move(callback), thread).GetFuture();
}

TaskFuture TaskFuture::OnAllFinished(std::vector<TaskFuture> &&futures)
{
    // we create a pseudo-task for the future group
}
} // namespace sparkle

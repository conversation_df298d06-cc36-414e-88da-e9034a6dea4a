
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineSystem.cmake:211 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/Llvm/x64/bin/clang-cl.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.exe"
      
      The C compiler identification is Clang, found in:
        D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/3.27.6/CompilerIdC/CMakeCCompilerId.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:1155 (message)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting C compiler /showIncludes prefix:
        Note: including file: .\\foo.h
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:1155 (message)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting C compiler /showIncludes prefix:
        Note: including file: .\\foo.h
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/Llvm/x64/bin/clang-cl.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.exe"
      
      The CXX compiler identification is Clang, found in:
        D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/3.27.6/CompilerIdCXX/CMakeCXXCompilerId.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:1155 (message)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        Note: including file: .\\foo.h
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:1155 (message)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        Note: including file: .\\foo.h
        
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-eqkzf9"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-eqkzf9"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Projects/Sparkle/build_cache/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Projects/Sparkle/build_cache/vcpkg"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-eqkzf9'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v cmTC_6e300
        [1/2] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo   /DWIN32 /D_WINDOWS  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_6e300.dir\\CMakeCCompilerABI.c.obj /FdCMakeFiles\\cmTC_6e300.dir\\ -c -- "C:\\Program Files\\CMake\\share\\cmake-3.27\\Modules\\CMakeCCompilerABI.c"
        [2/2] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_6e300.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_6e300.dir\\CMakeCCompilerABI.c.obj  /out:cmTC_6e300.exe /implib:cmTC_6e300.lib /pdb:cmTC_6e300.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console   && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-abt2hh"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-abt2hh"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Projects/Sparkle/build_cache/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Projects/Sparkle/build_cache/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-abt2hh'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v cmTC_2fac3
        [1/2] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP   /DWIN32 /D_WINDOWS /EHsc  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_2fac3.dir\\CMakeCXXCompilerABI.cpp.obj /FdCMakeFiles\\cmTC_2fac3.dir\\ -c -- "C:\\Program Files\\CMake\\share\\cmake-3.27\\Modules\\CMakeCXXCompilerABI.cpp"
        [2/2] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_2fac3.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_2fac3.dir\\CMakeCXXCompilerABI.cpp.obj  /out:cmTC_2fac3.exe /implib:cmTC_2fac3.lib /pdb:cmTC_2fac3.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console   && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/Internal/FeatureTesting.cmake:83 (_record_compiler_features)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/Compiler/CMakeCommonCompilerMacros.cmake:121 (_record_compiler_features_cxx)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompileFeatures.cmake:70 (cmake_record_cxx_compile_features)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:70 (CMAKE_DETERMINE_COMPILE_FEATURES)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-ljtwhf"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-ljtwhf"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Projects/Sparkle/build_cache/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Projects/Sparkle/build_cache/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-ljtwhf'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v cmTC_3f450
        [1/2] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP   /DWIN32 /D_WINDOWS /EHsc  /Zi /Ob0 /Od /RTC1 -MDd   -std:c++14 /showIncludes /FoCMakeFiles\\cmTC_3f450.dir\\feature_tests.cxx.obj /FdCMakeFiles\\cmTC_3f450.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-ljtwhf\\feature_tests.cxx
        [2/2] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_3f450.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_3f450.dir\\feature_tests.cxx.obj  /out:cmTC_3f450.exe /implib:cmTC_3f450.lib /pdb:cmTC_3f450.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/Internal/FeatureTesting.cmake:83 (_record_compiler_features)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/Compiler/CMakeCommonCompilerMacros.cmake:129 (_record_compiler_features_cxx)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeDetermineCompileFeatures.cmake:70 (cmake_record_cxx_compile_features)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeTestCXXCompiler.cmake:70 (CMAKE_DETERMINE_COMPILE_FEATURES)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-a4by9n"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-a4by9n"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Projects/Sparkle/build_cache/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Projects/Sparkle/build_cache/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-a4by9n'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v cmTC_b7d66
        [1/2] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP   /DWIN32 /D_WINDOWS /EHsc  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_b7d66.dir\\feature_tests.cxx.obj /FdCMakeFiles\\cmTC_b7d66.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-a4by9n\\feature_tests.cxx
        [2/2] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_b7d66.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_b7d66.dir\\feature_tests.cxx.obj  /out:cmTC_b7d66.exe /implib:cmTC_b7d66.lib /pdb:cmTC_b7d66.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:132 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:132 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "build_cache/vcpkg/scripts/buildsystems/vcpkg.cmake:894 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "build_cache/vcpkg/installed/x64-windows/share/glfw3/glfw3Config.cmake:2 (find_dependency)"
      - "build_cache/vcpkg/scripts/buildsystems/vcpkg.cmake:894 (_find_package)"
      - "CMakeLists.txt:282 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-p5xgyc"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-p5xgyc"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Projects/Sparkle/build_cache/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Projects/Sparkle/build_cache/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-p5xgyc'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v cmTC_9632b
        [1/2] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -DCMAKE_HAVE_LIBC_PTHREAD  /DWIN32 /D_WINDOWS  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_9632b.dir\\src.c.obj /FdCMakeFiles\\cmTC_9632b.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-p5xgyc\\src.c
        FAILED: CMakeFiles/cmTC_9632b.dir/src.c.obj 
        C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -DCMAKE_HAVE_LIBC_PTHREAD  /DWIN32 /D_WINDOWS  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_9632b.dir\\src.c.obj /FdCMakeFiles\\cmTC_9632b.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-p5xgyc\\src.c
        D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-p5xgyc\\src.c(1,10): fatal error: 'pthread.h' file not found
            1 | #include <pthread.h>
              |          ^~~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "build_cache/vcpkg/scripts/buildsystems/vcpkg.cmake:894 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "build_cache/vcpkg/installed/x64-windows/share/glfw3/glfw3Config.cmake:2 (find_dependency)"
      - "build_cache/vcpkg/scripts/buildsystems/vcpkg.cmake:894 (_find_package)"
      - "CMakeLists.txt:282 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-oqxwvs"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-oqxwvs"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Projects/Sparkle/build_cache/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Projects/Sparkle/build_cache/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-oqxwvs'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v cmTC_51eeb
        [1/2] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo   /DWIN32 /D_WINDOWS -DCHECK_FUNCTION_EXISTS=pthread_create /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_51eeb.dir\\CheckFunctionExists.c.obj /FdCMakeFiles\\cmTC_51eeb.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-oqxwvs\\CheckFunctionExists.c
        [2/2] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_51eeb.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_51eeb.dir\\CheckFunctionExists.c.obj  /out:cmTC_51eeb.exe /implib:cmTC_51eeb.lib /pdb:cmTC_51eeb.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthreads.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        FAILED: cmTC_51eeb.exe 
        cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_51eeb.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_51eeb.dir\\CheckFunctionExists.c.obj  /out:cmTC_51eeb.exe /implib:cmTC_51eeb.lib /pdb:cmTC_51eeb.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthreads.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        LINK Pass 1: command "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_51eeb.dir\\CheckFunctionExists.c.obj /out:cmTC_51eeb.exe /implib:cmTC_51eeb.lib /pdb:cmTC_51eeb.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_51eeb.dir/intermediate.manifest CMakeFiles\\cmTC_51eeb.dir/manifest.res" failed (exit code 1) with the following output:
        lld-link: error: could not open 'pthreads.lib': no such file or directory\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "build_cache/vcpkg/scripts/buildsystems/vcpkg.cmake:894 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "build_cache/vcpkg/installed/x64-windows/share/glfw3/glfw3Config.cmake:2 (find_dependency)"
      - "build_cache/vcpkg/scripts/buildsystems/vcpkg.cmake:894 (_find_package)"
      - "CMakeLists.txt:282 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-1gfk39"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-1gfk39"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Projects/Sparkle/build_cache/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Projects/Sparkle/build_cache/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-1gfk39'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v cmTC_f9d4b
        [1/2] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo   /DWIN32 /D_WINDOWS -DCHECK_FUNCTION_EXISTS=pthread_create /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_f9d4b.dir\\CheckFunctionExists.c.obj /FdCMakeFiles\\cmTC_f9d4b.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-1gfk39\\CheckFunctionExists.c
        [2/2] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_f9d4b.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_f9d4b.dir\\CheckFunctionExists.c.obj  /out:cmTC_f9d4b.exe /implib:cmTC_f9d4b.lib /pdb:cmTC_f9d4b.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        FAILED: cmTC_f9d4b.exe 
        cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_f9d4b.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_f9d4b.dir\\CheckFunctionExists.c.obj  /out:cmTC_f9d4b.exe /implib:cmTC_f9d4b.lib /pdb:cmTC_f9d4b.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  pthread.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        LINK Pass 1: command "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_f9d4b.dir\\CheckFunctionExists.c.obj /out:cmTC_f9d4b.exe /implib:cmTC_f9d4b.lib /pdb:cmTC_f9d4b.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTFILE:CMakeFiles\\cmTC_f9d4b.dir/intermediate.manifest CMakeFiles\\cmTC_f9d4b.dir/manifest.res" failed (exit code 1) with the following output:
        lld-link: error: could not open 'pthread.lib': no such file or directory\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "build_system/glfw/output/_deps/cpptrace-src/cmake/Autoconfig.cmake:10 (check_cxx_source_compiles)"
      - "build_system/glfw/output/_deps/cpptrace-src/cmake/Autoconfig.cmake:15 (check_support)"
      - "build_system/glfw/output/_deps/cpptrace-src/CMakeLists.txt:77 (include)"
    checks:
      - "Performing Test HAS_CXXABI"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-09mety"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-09mety"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Projects/Sparkle/build_cache/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Projects/Sparkle/build_cache/vcpkg"
    buildResult:
      variable: "HAS_CXXABI"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-09mety'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v cmTC_fbeaf
        [1/2] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DHAS_CXXABI -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\cmake /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_fbeaf.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_fbeaf.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-09mety\\src.cxx
        FAILED: CMakeFiles/cmTC_fbeaf.dir/src.cxx.obj 
        C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DHAS_CXXABI -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\cmake /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_fbeaf.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_fbeaf.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-09mety\\src.cxx
        In file included from D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-09mety\\src.cxx:1:
        D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\cmake\\has_cxxabi.cpp(1,10): fatal error: 'cxxabi.h' file not found
            1 | #include <cxxabi.h>
              |          ^~~~~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "build_system/glfw/output/_deps/cpptrace-src/cmake/Autoconfig.cmake:10 (check_cxx_source_compiles)"
      - "build_system/glfw/output/_deps/cpptrace-src/cmake/Autoconfig.cmake:19 (check_support)"
      - "build_system/glfw/output/_deps/cpptrace-src/CMakeLists.txt:77 (include)"
    checks:
      - "Performing Test HAS_ATTRIBUTE_PACKED"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-ax91i0"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-ax91i0"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Projects/Sparkle/build_cache/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Projects/Sparkle/build_cache/vcpkg"
    buildResult:
      variable: "HAS_ATTRIBUTE_PACKED"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-ax91i0'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v cmTC_5bc2f
        [1/2] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DHAS_ATTRIBUTE_PACKED -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\cmake /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_5bc2f.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_5bc2f.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-ax91i0\\src.cxx
        [2/2] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_5bc2f.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_5bc2f.dir\\src.cxx.obj  /out:cmTC_5bc2f.exe /implib:cmTC_5bc2f.lib /pdb:cmTC_5bc2f.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "build_system/glfw/output/_deps/cpptrace-src/cmake/Autoconfig.cmake:10 (check_cxx_source_compiles)"
      - "build_system/glfw/output/_deps/cpptrace-src/cmake/Autoconfig.cmake:26 (check_support)"
      - "build_system/glfw/output/_deps/cpptrace-src/CMakeLists.txt:77 (include)"
    checks:
      - "Performing Test HAS_STACKWALK"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-0rtyic"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-0rtyic"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Projects/Sparkle/build_cache/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Projects/Sparkle/build_cache/vcpkg"
    buildResult:
      variable: "HAS_STACKWALK"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/CMakeScratch/TryCompile-0rtyic'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v cmTC_efa84
        [1/2] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DHAS_STACKWALK -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\cmake /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0  /Zi /Ob0 /Od /RTC1 -MDd /showIncludes /FoCMakeFiles\\cmTC_efa84.dir\\src.cxx.obj /FdCMakeFiles\\cmTC_efa84.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\CMakeScratch\\TryCompile-0rtyic\\src.cxx
        [2/2] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\cmTC_efa84.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe /nologo CMakeFiles\\cmTC_efa84.dir\\src.cxx.obj  /out:cmTC_efa84.exe /implib:cmTC_efa84.lib /pdb:cmTC_efa84.pdb /version:0.0 /machine:x64  /debug /INCREMENTAL /subsystem:console  dbghelp.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): D:\\Projects\\Sparkle\\build_system\\..\\build_cache\\ninja\\ninja.exe -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:133 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.cpp.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe   -TP   /DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.cpp.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.cpp.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.cpp.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:138 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.27/Modules/CheckIPOSupported.cmake:266 (_ipo_run_language_check)"
      - "CMakeLists.txt:134 (check_ipo_supported)"
    directories:
      source: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'D:/Projects/Sparkle/build_system/glfw/output/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v
        [1/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\foo.dir\\foo.c.obj /FdCMakeFiles\\foo.dir\\foo.pdb -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c
        [2/4] C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe     /DWIN32 /D_WINDOWS /Ob0 /Od /RTC1 -MDd -Zi -flto=thin /showIncludes /FoCMakeFiles\\boo.dir\\main.c.obj /FdCMakeFiles\\boo.dir\\ -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c
        [3/4] cmd.exe /C "cd . && C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\llvm-lib.exe  /machine:x64 /out:foo.lib CMakeFiles\\foo.dir\\foo.c.obj  && cd ."
        [4/4] cmd.exe /C "cd . && "C:\\Program Files\\CMake\\bin\\cmake.exe" -E vs_link_exe --intdir=CMakeFiles\\boo.dir --rc=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe --mt=C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\mt.exe --manifests  -- C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\lld-link.exe  CMakeFiles\\boo.dir\\main.c.obj  /out:boo.exe /implib:boo.lib /pdb:boo.pdb /version:0.0 /machine:x64 /debug /INCREMENTAL /subsystem:console  foo.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib && cd ."
        
      exitCode: 0
...

[{"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\application\\AppConfig.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\application\\AppConfig.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\application\\AppConfig.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\application\\AppConfig.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\application\\AppFramework.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\application\\AppFramework.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\application\\AppFramework.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\application\\AppFramework.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\application\\NativeView.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\application\\NativeView.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\application\\NativeView.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\application\\NativeView.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\application\\RenderFramework.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\application\\RenderFramework.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\application\\RenderFramework.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\application\\RenderFramework.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\application\\UiManager.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\application\\UiManager.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\application\\UiManager.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\application\\UiManager.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\ConfigManager.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\ConfigManager.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\ConfigManager.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\ConfigManager.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\ConfigValue.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\ConfigValue.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\ConfigValue.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\ConfigValue.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\Event.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\Event.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\Event.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\Event.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\Exception.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\Exception.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\Exception.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\Exception.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\FileManager.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\FileManager.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\FileManager.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\FileManager.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\Logger.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\Logger.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\Logger.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\Logger.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\ThreadManager.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\ThreadManager.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\ThreadManager.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\ThreadManager.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\math\\AABB.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\math\\AABB.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\math\\AABB.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\math\\AABB.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\math\\Intersection.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\math\\Intersection.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\math\\Intersection.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\math\\Intersection.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\math\\Ray.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\math\\Ray.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\math\\Ray.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\math\\Ray.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\math\\Transform.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\math\\Transform.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\math\\Transform.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\math\\Transform.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\task\\TaskFuture.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\task\\TaskFuture.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\task\\TaskFuture.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\task\\TaskFuture.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\core\\task\\TaskManager.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\core\\task\\TaskManager.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\core\\task\\TaskManager.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\core\\task\\TaskManager.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\io\\Image.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\io\\Image.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\io\\Image.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\io\\Image.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\io\\Mesh.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\io\\Mesh.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\io\\Mesh.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\io\\Mesh.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\io\\StdFileManager.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\io\\StdFileManager.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\io\\StdFileManager.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\io\\StdFileManager.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\io\\scene\\GLTFLoader.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\io\\scene\\GLTFLoader.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\io\\scene\\GLTFLoader.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\io\\scene\\GLTFLoader.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\io\\scene\\SceneDataFactory.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\io\\scene\\SceneDataFactory.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\io\\scene\\SceneDataFactory.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\io\\scene\\SceneDataFactory.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\io\\scene\\USDLoader.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\io\\scene\\USDLoader.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\io\\scene\\USDLoader.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\io\\scene\\USDLoader.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\platform\\linux\\LinuxThreadUtils.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\platform\\linux\\LinuxThreadUtils.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\platform\\linux\\LinuxThreadUtils.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\platform\\linux\\LinuxThreadUtils.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\platform\\windows\\WindowsThreadUtils.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\platform\\windows\\WindowsThreadUtils.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\platform\\windows\\WindowsThreadUtils.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\platform\\windows\\WindowsThreadUtils.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\BindlessManager.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\BindlessManager.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\BindlessManager.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\BindlessManager.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\RenderConfig.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\RenderConfig.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\RenderConfig.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\RenderConfig.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\BlurPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\BlurPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\BlurPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\BlurPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\ClearTexturePass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\ClearTexturePass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\ClearTexturePass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\ClearTexturePass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\DepthPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\DepthPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\DepthPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\DepthPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\DirectionalLightingPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\DirectionalLightingPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\DirectionalLightingPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\DirectionalLightingPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\ForwardMeshPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\ForwardMeshPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\ForwardMeshPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\ForwardMeshPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\GBufferPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\GBufferPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\GBufferPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\GBufferPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\IBLBrdfPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\IBLBrdfPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\IBLBrdfPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\IBLBrdfPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\IBLDiffusePass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\IBLDiffusePass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\IBLDiffusePass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\IBLDiffusePass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\IBLPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\IBLPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\IBLPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\IBLPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\IBLSpecularPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\IBLSpecularPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\IBLSpecularPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\IBLSpecularPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\MeshPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\MeshPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\MeshPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\MeshPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\ScreenQuadPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\ScreenQuadPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\ScreenQuadPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\ScreenQuadPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\SkyBoxPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\SkyBoxPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\SkyBoxPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\SkyBoxPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\ToneMappingPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\ToneMappingPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\ToneMappingPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\ToneMappingPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\UiPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\UiPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\pass\\UiPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\pass\\UiPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\CameraRenderProxy.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\CameraRenderProxy.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\CameraRenderProxy.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\CameraRenderProxy.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\DirectionalLightRenderProxy.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\DirectionalLightRenderProxy.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\DirectionalLightRenderProxy.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\DirectionalLightRenderProxy.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\MaterialRenderProxy.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\MaterialRenderProxy.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\MaterialRenderProxy.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\MaterialRenderProxy.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\MeshRenderProxy.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\MeshRenderProxy.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\MeshRenderProxy.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\MeshRenderProxy.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\PrimitiveRenderProxy.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\PrimitiveRenderProxy.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\PrimitiveRenderProxy.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\PrimitiveRenderProxy.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\SceneRenderProxy.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\SceneRenderProxy.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\SceneRenderProxy.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\SceneRenderProxy.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\SkyRenderProxy.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\SkyRenderProxy.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\SkyRenderProxy.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\SkyRenderProxy.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\SphereRenderProxy.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\SphereRenderProxy.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\proxy\\SphereRenderProxy.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\proxy\\SphereRenderProxy.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\renderer\\CPURenderer.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\renderer\\CPURenderer.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\renderer\\CPURenderer.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\renderer\\CPURenderer.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\renderer\\DeferredRenderer.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\renderer\\DeferredRenderer.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\renderer\\DeferredRenderer.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\renderer\\DeferredRenderer.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\renderer\\ForwardRenderer.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\renderer\\ForwardRenderer.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\renderer\\ForwardRenderer.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\renderer\\ForwardRenderer.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\renderer\\GPURenderer.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\renderer\\GPURenderer.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\renderer\\GPURenderer.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\renderer\\GPURenderer.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\renderer\\Renderer.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\renderer\\Renderer.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\renderer\\Renderer.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\renderer\\Renderer.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\resource\\GBuffer.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\resource\\GBuffer.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\resource\\GBuffer.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\resource\\GBuffer.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\resource\\ImageBasedLighting.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\renderer\\resource\\ImageBasedLighting.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\renderer\\resource\\ImageBasedLighting.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\renderer\\resource\\ImageBasedLighting.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHI.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHI.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHI.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHI.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIBuffer.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIBuffer.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIBuffer.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIBuffer.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIComputePass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIComputePass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIComputePass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIComputePass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIConfig.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIConfig.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIConfig.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIConfig.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIImage.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIImage.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIImage.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIImage.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIRenderTarget.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIRenderTarget.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIRenderTarget.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIRenderTarget.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIResource.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIResource.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIResource.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIResource.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIResourceArray.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIResourceArray.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIResourceArray.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIResourceArray.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIShader.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIShader.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIShader.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIShader.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIUiHandler.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIUiHandler.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\RHIUiHandler.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\RHIUiHandler.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanBuffer.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanBuffer.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanBuffer.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanBuffer.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanCommandBuffer.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanCommandBuffer.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanCommandBuffer.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanCommandBuffer.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanComputePass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanComputePass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanComputePass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanComputePass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanContext.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanContext.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanContext.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanContext.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanDescriptorSet.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanDescriptorSet.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanDescriptorSet.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanDescriptorSet.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanDescriptorSetManager.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanDescriptorSetManager.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanDescriptorSetManager.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanDescriptorSetManager.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanFunctionLoader.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanFunctionLoader.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanFunctionLoader.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanFunctionLoader.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanImage.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanImage.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanImage.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanImage.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanMemory.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanMemory.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanMemory.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanMemory.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanPipelineState.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanPipelineState.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanPipelineState.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanPipelineState.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanRHI.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanRHI.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanRHI.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanRHI.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanRayTracing.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanRayTracing.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanRayTracing.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanRayTracing.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanRenderPass.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanRenderPass.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanRenderPass.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanRenderPass.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanRenderTarget.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanRenderTarget.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanRenderTarget.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanRenderTarget.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanResourceArray.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanResourceArray.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanResourceArray.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanResourceArray.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanShader.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanShader.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanShader.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanShader.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanSwapChain.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanSwapChain.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanSwapChain.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanSwapChain.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanSynchronization.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanSynchronization.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanSynchronization.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanSynchronization.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanTimer.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanTimer.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanTimer.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanTimer.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanUi.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanUi.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\rhi\\vulkan\\VulkanUi.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\rhi\\vulkan\\VulkanUi.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\Scene.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\Scene.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\Scene.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\Scene.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\SceneManager.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\SceneManager.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\SceneManager.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\SceneManager.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\SceneNode.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\SceneNode.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\SceneNode.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\SceneNode.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\Component.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\Component.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\Component.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\Component.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\RenderableComponent.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\RenderableComponent.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\RenderableComponent.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\RenderableComponent.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\camera\\CameraComponent.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\camera\\CameraComponent.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\camera\\CameraComponent.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\camera\\CameraComponent.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\camera\\OrbitCameraComponent.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\camera\\OrbitCameraComponent.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\camera\\OrbitCameraComponent.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\camera\\OrbitCameraComponent.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\light\\DirectionalLight.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\light\\DirectionalLight.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\light\\DirectionalLight.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\light\\DirectionalLight.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\light\\LightSource.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\light\\LightSource.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\light\\LightSource.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\light\\LightSource.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\light\\SkyLight.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\light\\SkyLight.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\light\\SkyLight.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\light\\SkyLight.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\primitive\\MeshPrimitive.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\primitive\\MeshPrimitive.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\primitive\\MeshPrimitive.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\primitive\\MeshPrimitive.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\primitive\\PrimitiveComponent.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\primitive\\PrimitiveComponent.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\primitive\\PrimitiveComponent.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\primitive\\PrimitiveComponent.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\primitive\\SpherePrimitive.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\primitive\\SpherePrimitive.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\component\\primitive\\SpherePrimitive.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\component\\primitive\\SpherePrimitive.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\material\\Material.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\material\\Material.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\material\\Material.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\material\\Material.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\libraries\\source\\scene\\material\\MaterialManager.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\libraries\\source\\scene\\material\\MaterialManager.cpp", "file": "D:\\Projects\\Sparkle\\libraries\\source\\scene\\material\\MaterialManager.cpp", "output": "CMakeFiles\\sparkle.dir\\libraries\\source\\scene\\material\\MaterialManager.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\frameworks\\source\\glfw\\GLFWMain.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\frameworks\\source\\glfw\\GLFWMain.cpp", "file": "D:\\Projects\\Sparkle\\frameworks\\source\\glfw\\GLFWMain.cpp", "output": "CMakeFiles\\sparkle.dir\\frameworks\\source\\glfw\\GLFWMain.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DENABLE_PROFILER=0 -DENABLE_VULKAN -DFRAMEWORK_GLFW=1 -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DNOMINMAX -DPLATFORM_WINDOWS=1 -DVK_USE_PLATFORM_WIN32_KHR -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\libraries\\include -ID:\\Projects\\Sparkle\\frameworks\\include -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /FI core/GlobalMacro.h -Wall -Wextra -Wpedantic -Werror -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-c99-extensions -Wno-c++20-compat -Wno-extra-semi-stmt -Wno-unused-macros -Wno-covered-switch-default -Wno-switch-enum -Wno-cast-function-type-strict -Wno-unsafe-buffer-usage -Wno-global-constructors -Wno-exit-time-destructors -Wno-unknown-pragmas /FoCMakeFiles\\sparkle.dir\\frameworks\\source\\glfw\\GLFWNativeView.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\frameworks\\source\\glfw\\GLFWNativeView.cpp", "file": "D:\\Projects\\Sparkle\\frameworks\\source\\glfw\\GLFWNativeView.cpp", "output": "CMakeFiles\\sparkle.dir\\frameworks\\source\\glfw\\GLFWNativeView.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\crc32.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\crc32.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\crc32.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\crc32.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\digest.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\digest.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\digest.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\digest.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\keccak.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\keccak.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\keccak.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\keccak.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\md5.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\md5.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\md5.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\md5.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\murmur3.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\murmur3.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\murmur3.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\murmur3.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\sha1.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\sha1.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\sha1.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\sha1.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\sha256.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\sha256.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\sha256.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\sha256.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\sha3.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\sha3.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\hash-library\\src\\sha3.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\hash-library\\src\\sha3.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\imgui.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\imgui\\imgui.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\imgui\\imgui.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\imgui.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\imgui_demo.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\imgui\\imgui_demo.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\imgui\\imgui_demo.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\imgui_demo.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\imgui_draw.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\imgui\\imgui_draw.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\imgui\\imgui_draw.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\imgui_draw.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\imgui_tables.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\imgui\\imgui_tables.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\imgui\\imgui_tables.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\imgui_tables.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\imgui_widgets.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\imgui\\imgui_widgets.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\imgui\\imgui_widgets.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\imgui_widgets.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\misc\\cpp\\imgui_stdlib.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp\\imgui_stdlib.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp\\imgui_stdlib.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\misc\\cpp\\imgui_stdlib.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\spirv_reflect\\spirv_reflect.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\spirv_reflect\\spirv_reflect.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\spirv_reflect\\spirv_reflect.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\spirv_reflect\\spirv_reflect.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\backends\\imgui_impl_glfw.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\imgui\\backends\\imgui_impl_glfw.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\imgui\\backends\\imgui_impl_glfw.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\backends\\imgui_impl_glfw.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_STATIC_DEFINE -DGLFW_DLL -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES -DVULKAN_USE_VOLK=1 -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\stb -imsvcD:\\Projects\\Sparkle\\thirdparty\\argparse\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\bvh\\src -imsvcD:\\Projects\\Sparkle\\thirdparty\\thread-pool\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\spdlog\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\vma\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinygltf -imsvcD:\\Projects\\Sparkle\\thirdparty\\eigen -imsvcD:\\Projects\\Sparkle\\thirdparty\\volk -imsvcD:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fast_float\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\json\\single_include -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\backends -imsvcD:\\Projects\\Sparkle\\thirdparty\\imgui\\misc\\cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\spirv_reflect -imsvcD:\\Projects\\Sparkle\\thirdparty\\magic_enum\\include -imsvcD:\\Projects\\Sparkle\\thirdparty\\xoshiro_cpp -imsvcD:\\Projects\\Sparkle\\thirdparty\\hash-library\\include -imsvcD:\\SDKs\\VulkanSDK\\1.4.313.0\\Include -imsvcD:\\Projects\\Sparkle\\build_cache\\vcpkg\\installed\\x64-windows\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /Fothirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\backends\\imgui_impl_vulkan.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\imgui\\backends\\imgui_impl_vulkan.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\imgui\\backends\\imgui_impl_vulkan.cpp", "output": "thirdparty\\CMakeFiles\\sparkle_thirdparty.dir\\imgui\\backends\\imgui_impl_vulkan.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\asset-resolution.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\asset-resolution.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\asset-resolution.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\asset-resolution.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tinyusdz.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tinyusdz.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tinyusdz.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tinyusdz.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\xform.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\xform.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\xform.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\xform.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\performance.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\performance.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\performance.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\performance.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\ascii-parser.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\ascii-parser.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\ascii-parser.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\ascii-parser.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\ascii-parser-basetype.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\ascii-parser-basetype.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\ascii-parser-basetype.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\ascii-parser-basetype.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\ascii-parser-timesamples.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\ascii-parser-timesamples.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\ascii-parser-timesamples.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\ascii-parser-timesamples.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\ascii-parser-timesamples-array.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\ascii-parser-timesamples-array.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\ascii-parser-timesamples-array.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\ascii-parser-timesamples-array.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\audio-loader.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\audio-loader.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\audio-loader.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\audio-loader.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usda-reader.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usda-reader.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usda-reader.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usda-reader.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdc-reader.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdc-reader.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdc-reader.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdc-reader.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usda-writer.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usda-writer.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usda-writer.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usda-writer.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdc-writer.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdc-writer.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdc-writer.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdc-writer.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\composition.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\composition.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\composition.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\composition.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\crate-reader.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\crate-reader.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\crate-reader.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\crate-reader.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\crate-format.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\crate-format.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\crate-format.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\crate-format.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\crate-writer.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\crate-writer.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\crate-writer.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\crate-writer.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\crate-pprint.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\crate-pprint.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\crate-pprint.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\crate-pprint.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\path-util.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\path-util.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\path-util.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\path-util.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\prim-reconstruct.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\prim-reconstruct.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\prim-reconstruct.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\prim-reconstruct.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\prim-composition.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\prim-composition.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\prim-composition.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\prim-composition.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\prim-types.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\prim-types.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\prim-types.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\prim-types.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\primvar.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\primvar.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\primvar.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\primvar.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\str-util.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\str-util.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\str-util.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\str-util.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\value-pprint.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\value-pprint.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\value-pprint.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\value-pprint.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\value-types.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\value-types.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\value-types.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\value-types.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tiny-format.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tiny-format.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tiny-format.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tiny-format.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\io-util.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\io-util.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\io-util.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\io-util.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\image-loader.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\image-loader.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\image-loader.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\image-loader.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\image-writer.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\image-writer.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\image-writer.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\image-writer.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\image-util.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\image-util.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\image-util.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\image-util.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\linear-algebra.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\linear-algebra.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\linear-algebra.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\linear-algebra.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdGeom.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdGeom.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdGeom.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdGeom.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdSkel.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdSkel.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdSkel.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdSkel.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdShade.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdShade.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdShade.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdShade.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdLux.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdLux.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdLux.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdLux.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdMtlx.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdMtlx.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdMtlx.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdMtlx.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdObj.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdObj.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdObj.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdObj.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\pprinter.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\pprinter.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\pprinter.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\pprinter.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\stage.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\stage.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\stage.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\stage.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\facial.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\facial.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\facial.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\facial.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\prim-apply.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\prim-apply.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\prim-apply.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\prim-apply.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\scene-access.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\scene-access.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\scene-access.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\scene-access.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\attribute-eval.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\attribute-eval.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\attribute-eval.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\attribute-eval.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\attribute-eval-typed.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\attribute-eval-typed.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\attribute-eval-typed.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\attribute-eval-typed.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\attribute-eval-typed-animatable.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\attribute-eval-typed-animatable.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\attribute-eval-typed-animatable.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\attribute-eval-typed-animatable.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\attribute-eval-typed-fallback.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\attribute-eval-typed-fallback.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\attribute-eval-typed-fallback.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\attribute-eval-typed-fallback.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\attribute-eval-typed-animatable-fallback.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\attribute-eval-typed-animatable-fallback.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\attribute-eval-typed-animatable-fallback.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\attribute-eval-typed-animatable-fallback.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\obj-export.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\obj-export.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\obj-export.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\obj-export.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\usd-export.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\usd-export.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\usd-export.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\usd-export.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\shader-network.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\shader-network.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\shader-network.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\shader-network.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\render-data.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\render-data.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\render-data.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\render-data.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\texture-util.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\texture-util.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\tydra\\texture-util.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\tydra\\texture-util.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\pxr-compat.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\pxr-compat.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\pxr-compat.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\pxr-compat.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj -Weverything -Wno-poison-system-directories -Wno-padded -Wno-c++98-compat -Wno-c++98-compat-pedantic -Wno-documentation -Wno-unused-member-function -Wno-unsafe-buffer-usage -Wno-switch-default /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdVox.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdVox.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\usdVox.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\usdVox.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\integerCoding.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\integerCoding.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\integerCoding.cpp", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\integerCoding.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\lz4-compression.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\lz4-compression.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\lz4-compression.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\lz4-compression.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /Zi /Ob0 /Od /RTC1 -MDd /bigobj /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\lz4\\lz4.c.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\lz4\\lz4.c", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\lz4\\lz4.c", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\lz4\\lz4.c.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -DFPNG_NO_SSE=1 -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\external\\fpng.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fpng.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\fpng.cpp", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\external\\fpng.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\external\\pugixml.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\pugixml.cpp", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\pugixml.cpp", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\external\\pugixml.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\external\\tiny_obj_loader.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\tiny_obj_loader.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\tiny_obj_loader.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\external\\tiny_obj_loader.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd /bigobj /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\external\\tinyexr.cc.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\tinyexr.cc", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\tinyexr.cc", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\external\\tinyexr.cc.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -DTINYUSDZ_USE_USDMTLX -DTINYUSDZ_USE_USDOBJ -DTINYUSDZ_USE_USDVOX -DTINYUSDZ_WITH_AUDIO -DTINYUSDZ_WITH_COLORIO -DTINYUSDZ_WITH_EXR -DTINYUSDZ_WITH_TYDRA -D_LARGEFILE64_SOURCE=1 -ID:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src /DWIN32 /D_WINDOWS /Zi /Ob0 /Od /RTC1 -MDd /bigobj /Fothirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\external\\miniz.c.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\miniz.c", "file": "D:\\Projects\\Sparkle\\thirdparty\\tinyusdz\\src\\external\\miniz.c", "output": "thirdparty\\tinyusdz\\CMakeFiles\\tinyusdz_static.dir\\src\\external\\miniz.c.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DMI_CMAKE_BUILD_TYPE=debug -ID:\\Projects\\Sparkle\\thirdparty\\mimalloc\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd -W -Wno-deprecated -fno-builtin-malloc /Zc:__cplusplus -march=haswell -mavx2 -w /Fothirdparty\\mimalloc\\CMakeFiles\\mimalloc-obj.dir\\src\\static.c.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\thirdparty\\mimalloc\\src\\static.c", "file": "D:\\Projects\\Sparkle\\thirdparty\\mimalloc\\src\\static.c", "output": "thirdparty\\mimalloc\\CMakeFiles\\mimalloc-obj.dir\\src\\static.c.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\elf.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\elf.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\elf.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\elf.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\mach-o.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\mach-o.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\mach-o.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\mach-o.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\module_base.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\module_base.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\module_base.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\module_base.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\object.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\object.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\object.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\object.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\pe.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\pe.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\pe.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\pe.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\safe_dl.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\safe_dl.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\binary\\safe_dl.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\binary\\safe_dl.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\cpptrace.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\cpptrace.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\cpptrace.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\cpptrace.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\ctrace.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\ctrace.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\ctrace.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\ctrace.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\exceptions.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\exceptions.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\exceptions.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\exceptions.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\from_current.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\from_current.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\from_current.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\from_current.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\formatting.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\formatting.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\formatting.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\formatting.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\options.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\options.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\options.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\options.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\demangle\\demangle_with_cxxabi.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\demangle\\demangle_with_cxxabi.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\demangle\\demangle_with_cxxabi.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\demangle\\demangle_with_cxxabi.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\demangle\\demangle_with_nothing.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\demangle\\demangle_with_nothing.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\demangle\\demangle_with_nothing.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\demangle\\demangle_with_nothing.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\demangle\\demangle_with_winapi.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\demangle\\demangle_with_winapi.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\demangle\\demangle_with_winapi.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\demangle\\demangle_with_winapi.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\jit\\jit_objects.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\jit\\jit_objects.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\jit\\jit_objects.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\jit\\jit_objects.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\snippets\\snippet.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\snippets\\snippet.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\snippets\\snippet.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\snippets\\snippet.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\dwarf\\debug_map_resolver.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\dwarf\\debug_map_resolver.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\dwarf\\debug_map_resolver.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\dwarf\\debug_map_resolver.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\dwarf\\dwarf_options.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\dwarf\\dwarf_options.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\dwarf\\dwarf_options.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\dwarf\\dwarf_options.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\dwarf\\dwarf_resolver.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\dwarf\\dwarf_resolver.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\dwarf\\dwarf_resolver.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\dwarf\\dwarf_resolver.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_core.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_core.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_core.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_core.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_addr2line.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_addr2line.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_addr2line.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_addr2line.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_dbghelp.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_dbghelp.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_dbghelp.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_dbghelp.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_dl.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_dl.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_dl.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_dl.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_libbacktrace.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_libbacktrace.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_libbacktrace.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_libbacktrace.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_libdwarf.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_libdwarf.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_libdwarf.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_libdwarf.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_nothing.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_nothing.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\symbols\\symbols_with_nothing.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\symbols\\symbols_with_nothing.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_dbghelp.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_dbghelp.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_dbghelp.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_dbghelp.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_execinfo.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_execinfo.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_execinfo.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_execinfo.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_libunwind.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_libunwind.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_libunwind.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_libunwind.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_nothing.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_nothing.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_nothing.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_nothing.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_unwind.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_unwind.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_unwind.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_unwind.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_winapi.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_winapi.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\unwind\\unwind_with_winapi.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\unwind\\unwind_with_winapi.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\io\\file.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\io\\file.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\io\\file.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\io\\file.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\io\\memory_file_view.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\io\\memory_file_view.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\io\\memory_file_view.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\io\\memory_file_view.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\error.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\error.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\error.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\error.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\microfmt.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\microfmt.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\microfmt.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\microfmt.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\string_view.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\string_view.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\string_view.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\string_view.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\utils.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\utils.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\utils\\utils.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\utils\\utils.cpp.obj"}, {"directory": "D:/Projects/Sparkle/build_system/glfw/output", "command": "C:\\PROGRA~1\\MICROS~3\\2022\\PROFES~1\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe  /nologo -TP -DCPPTRACE_DEMANGLE_WITH_WINAPI -DCPPTRACE_GET_SYMBOLS_WITH_DBGHELP -DCPPTRACE_STATIC_DEFINE -DCPPTRACE_UNWIND_WITH_DBGHELP -DHAS_ATTRIBUTE_PACKED -DNOMINMAX -ID:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\include -imsvcD:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-build\\include /DWIN32 /D_WINDOWS /EHsc /MP /Zc:preprocessor /W0 /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd -w /Fo_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\platform\\dbghelp_utils.cpp.obj /FdTARGET_COMPILE_PDB -c -- D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\platform\\dbghelp_utils.cpp", "file": "D:\\Projects\\Sparkle\\build_system\\glfw\\output\\_deps\\cpptrace-src\\src\\platform\\dbghelp_utils.cpp", "output": "_deps\\cpptrace-build\\CMakeFiles\\cpptrace-lib.dir\\src\\platform\\dbghelp_utils.cpp.obj"}]
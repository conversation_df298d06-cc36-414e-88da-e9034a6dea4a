#pragma once

#include "core/ThreadManager.h"
#include "core/task/AsyncTask.h"

#include <future>

namespace sparkle
{
// A future is created along with a task to provide its completion status.
// You can use it to chain tasks together.
// The future is not bound to tasks, so it is safe to use even after the task is destroyed.
// It is also safe to copy as it is reference counted.
// It is not suitable for heavy use due to cross-thread safety measurements.
class TaskFuture
{
public:
    explicit TaskFuture(AsyncTask::TargetThread target_thread) : target_thread_(target_thread)
    {
    }

    explicit TaskFuture(std::future<void> &&future) : future_(std::move(future))
    {
    }

    void Setup(std::future<void> &&future)
    {
        future_ = std::move(future);
    }

    [[nodiscard]] ThreadName GetTargetThreadName() const
    {
        switch (target_thread_)
        {
        case AsyncTask::TargetThread::Inherit:
            return ThreadManager::CurrentThread();
        case AsyncTask::TargetThread::Main:
            return ThreadName::Main;
        case AsyncTask::TargetThread::Render:
            return ThreadName::Render;
        case AsyncTask::TargetThread::Workder:
            return ThreadName::Worker;
        default:
            UnImplemented(target_thread_);
            return ThreadName::Main;
        }
    }

    TaskFuture &Then(std::function<void()> &&callback, AsyncTask::TargetThread thread);

    void Wait() const
    {
        if (!future_.valid())
        {
            return;
        }

        future_.wait();
    }

    [[nodiscard]] static TaskFuture OnAllFinished(std::vector<TaskFuture> &&futures);

private:
    std::future<void> future_;
    std::vector<AsyncTask> callbacks_;
    AsyncTask::TargetThread target_thread_ = AsyncTask::TargetThread::Inherit;
};
} // namespace sparkle

#pragma once

#include <functional>
#include <memory>

namespace sparkle
{
class TaskFuture;

class AsyncTask
{
public:
    enum class TargetThread : uint8_t
    {
        Inherit,
        Main,
        Render,
        Workder,
    };

    explicit AsyncTask(std::function<void()> &&task, TargetThread target_thread);

    void Dispatch();

    TaskFuture &Then(std::function<void()> &&task, TargetThread target_thread = TargetThread::Inherit);

    [[nodiscard]] TaskFuture &GetFuture() const
    {
        return *future_;
    }

    static void *operator new(size_t) = delete;
    static void *operator new[](size_t) = delete;
    static void operator delete(void *) = delete;
    static void operator delete[](void *) = delete;

private:
    std::function<void()> task_;

    std::unique_ptr<TaskFuture> future_;

    friend class TaskManager;
};
} // namespace sparkle
